import { showGlobalLoading, hideGlobalLoading } from './loading';

interface RequestLoadingConfig {
    /** 是否启用全局loading */
    enabled?: boolean;
    /** 自定义loading文本 */
    text?: string;
    /** 最小显示时间（毫秒），防止loading闪烁 */
    minDuration?: number;
    /** 最大显示时间（毫秒），防止loading卡死 */
    maxDuration?: number;
}

interface RequestConfig {
    /** 请求URL */
    url?: string;
    /** 请求方法 */
    method?: string;
    /** 自定义loading配置 */
    loading?: RequestLoadingConfig | boolean;
    /** 其他请求配置 */
    [key: string]: any;
}

class RequestLoadingManager {
    private loadingMap = new Map<string, { instance: any; startTime: number; timer?: NodeJS.Timeout }>();
    private defaultConfig: RequestLoadingConfig = {
        enabled: true,
        text: '请求处理中...',
        minDuration: 300,
        maxDuration: 30000
    };

    /**
     * 生成请求的唯一标识
     */
    private getRequestKey(config: RequestConfig): string {
        return `${config.method?.toUpperCase() || 'GET'}_${config.url || ''}`;
    }

    /**
     * 请求开始时显示loading
     */
    showLoading(config: RequestConfig): void {
        const loadingConfig = this.getLoadingConfig(config);
        if (!loadingConfig.enabled) return;

        const requestKey = this.getRequestKey(config);
        
        // 如果已经有相同的请求在loading，不重复显示
        if (this.loadingMap.has(requestKey)) return;

        const startTime = Date.now();
        const instance = showGlobalLoading({
            text: loadingConfig.text,
            duration: 0 // 手动控制关闭
        });

        // 设置最大显示时间，防止loading卡死
        const maxTimer = setTimeout(() => {
            this.hideLoading(config);
        }, loadingConfig.maxDuration || this.defaultConfig.maxDuration!);

        this.loadingMap.set(requestKey, {
            instance,
            startTime,
            timer: maxTimer
        });
    }

    /**
     * 请求结束时隐藏loading
     */
    hideLoading(config: RequestConfig): void {
        const requestKey = this.getRequestKey(config);
        const loadingInfo = this.loadingMap.get(requestKey);
        
        if (!loadingInfo) return;

        const { instance, startTime, timer } = loadingInfo;
        const loadingConfig = this.getLoadingConfig(config);
        const elapsedTime = Date.now() - startTime;
        const minDuration = loadingConfig.minDuration || this.defaultConfig.minDuration!;

        // 清除最大时间定时器
        if (timer) {
            clearTimeout(timer);
        }

        // 如果显示时间小于最小时间，延迟关闭以防止闪烁
        if (elapsedTime < minDuration) {
            setTimeout(() => {
                instance.close();
                this.loadingMap.delete(requestKey);
            }, minDuration - elapsedTime);
        } else {
            instance.close();
            this.loadingMap.delete(requestKey);
        }
    }

    /**
     * 获取loading配置
     */
    private getLoadingConfig(config: RequestConfig): RequestLoadingConfig {
        const { loading } = config;
        
        if (loading === false) {
            return { enabled: false };
        }
        
        if (loading === true) {
            return { ...this.defaultConfig };
        }
        
        if (typeof loading === 'object') {
            return { ...this.defaultConfig, ...loading };
        }
        
        // 默认不启用，需要显式配置
        return { enabled: false };
    }

    /**
     * 清除所有loading
     */
    clearAllLoading(): void {
        this.loadingMap.forEach(({ instance, timer }) => {
            if (timer) clearTimeout(timer);
            instance.close();
        });
        this.loadingMap.clear();
    }

    /**
     * 设置默认配置
     */
    setDefaultConfig(config: Partial<RequestLoadingConfig>): void {
        this.defaultConfig = { ...this.defaultConfig, ...config };
    }
}

// 创建全局实例
const requestLoadingManager = new RequestLoadingManager();

/**
 * 请求拦截器 - 显示loading
 */
export const requestLoadingInterceptor = {
    onRequest: (config: RequestConfig) => {
        requestLoadingManager.showLoading(config);
        return config;
    },
    onResponse: (response: any) => {
        requestLoadingManager.hideLoading(response.config);
        return response;
    },
    onError: (error: any) => {
        if (error.config) {
            requestLoadingManager.hideLoading(error.config);
        }
        return Promise.reject(error);
    }
};

/**
 * 手动控制请求loading
 */
export const requestLoading = {
    show: (config: RequestConfig) => requestLoadingManager.showLoading(config),
    hide: (config: RequestConfig) => requestLoadingManager.hideLoading(config),
    clear: () => requestLoadingManager.clearAllLoading(),
    setDefault: (config: Partial<RequestLoadingConfig>) => requestLoadingManager.setDefaultConfig(config)
};

export default requestLoadingManager;
