import useStore from '@/store/modules/main';
import { h } from 'vue';
import { NButton } from 'naive-ui';

const store = useStore();
/**
 * 文件权限校验和二次确认的工具函数
 */
class FilePermissionUtils {
    static async checkIsAdmin(): Promise<{ isAdmin: boolean }> {
        //  判断用户是否为管理员
        try {
            const res = await api.sass.api.v1.genericInterface.getUserRoles(store.userInfo.id);
            const isAdmin = res.data.includes('JTWJGLY') || res.data.includes('ZGSWJGLY');
            return { isAdmin: isAdmin };
        } catch (error) {
            console.error('检查管理员权限失败:', error);
            return { isAdmin: false };
        }
    }

    /**
     * 检查是否有进行中的申请流程（状态为1）
     */
    static async checkOngoingApplication(row: any): Promise<{ hasOngoing: boolean }> {
        const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
            documentId: row.id,
            userId: store.userInfo.id
        });

        // 检查是否有状态为1的记录
        const ongoingRecords = (res.data.list || []).filter((record: any) => record.status === 1);

        return {
            hasOngoing: ongoingRecords.length > 0
        };
    }

    /**
     * 检查查阅权限
     */
    static async checkViewPermission(row: any): Promise<{ hasPermission: boolean }> {
        const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
            documentId: row.id,
            userId: store.userInfo.id
        });

        // 只保留 status 为 2 或 3 的记录
        const list = (res.data.list || []).filter((record: any) => record.status === 2 || record.status === 3);
        // 先收集所有查阅权限记录
        const viewPermissions: Array<{ fileForm: number, filePermission: number, inventoryId: string }> = [];

        for (const record of list) {
            if (((record.fileForm === 1 && record.filePermission === 1) ||
                (record.fileForm === 1 && record.filePermission === 2)) &&
                record.isUsed !== true) {
                viewPermissions.push({
                    fileForm: record.fileForm,
                    filePermission: record.filePermission,
                    inventoryId: record.inventoryId
                });
            }
        }

        // 如果有查阅权限记录，直接返回有权限
        if (viewPermissions.length > 0) {
            return {
                hasPermission: true
            };
        }

        return {
            hasPermission: false
        };
    }

    /**
     * 检查下载权限
     */
    static async checkDownloadPermission(row: any, fileForm?: number, filePermission?: number): Promise<{ hasPermission: boolean; fileForm?: number; filePermission?: number }> {
        const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
            documentId: row.id,
            userId: store.userInfo.id
        });

        // 根据不同的权限类型使用不同的 status 过滤逻辑
        const list = (res.data.list || []).filter((record: any) => {
            // 对于 fileForm=1 且 filePermission=2，只要 status !== 4 就可以使用
            if (record.fileForm === 1 && record.filePermission === 2) {
                return record.status !== 4;
            }
            // 对于纸质文件一次下载权限(fileForm=2, filePermission=3)使用 status > 1
            if (record.fileForm === 2 && record.filePermission === 3) {
                return record.status > 1;
            }
            // 其他权限使用 status === 2 || status === 3
            return record.status === 2 || record.status === 3;
        });
        // 如果传入了特定参数，直接返回有权限
        if (fileForm !== undefined && filePermission !== undefined) {
            return {
                hasPermission: true,
                fileForm: fileForm,
                filePermission: filePermission
            };
        }

        // 先收集所有下载权限记录
        const downloadPermissions: Array<{ fileForm: number, filePermission: number, inventoryId: string }> = [];

        for (const record of list) {
            if (((record.fileForm === 1 && record.filePermission === 2) ||
                (record.fileForm === 1 && record.filePermission === 3) ||
                (record.fileForm === 2 && record.filePermission === 3)) &&
                record.isUsed !== true) {
                downloadPermissions.push({
                    fileForm: record.fileForm,
                    filePermission: record.filePermission,
                    inventoryId: record.inventoryId
                });
            }
        }

        // 如果有下载权限记录，直接返回有权限
        if (downloadPermissions.length > 0) {
            return {
                hasPermission: true,
                fileForm: downloadPermissions[0].fileForm,
                filePermission: downloadPermissions[0].filePermission
            };
        }

        return {
            hasPermission: false,
            fileForm: undefined,
            filePermission: undefined
        };
    }

    /**
     * 显示权限校验对话框
     */
    static showPermissionDialog(
        type: 'view' | 'download',
        hasPermission: boolean,
        onConfirm: (method: string, fileForm?: number, filePermission?: number, inventoryId?: string) => void,
        row: any,
        fileForm?: number | Array<{ fileForm: number, filePermission: number, inventoryId: string }>,
        filePermission?: number,
        fileType?: number
    ) {
        if (hasPermission) {
            // 有权限，显示方式选择对话框（下载）
            if (type === 'download') {
                // 检查是否传入了权限数组
                if (Array.isArray(fileForm)) {
                    // 根据权限数组动态生成按钮
                    const buttons: any[] = [];

                    // 遍历权限数组，为每个权限生成对应的按钮
                    for (const permission of fileForm) {
                        if (permission.fileForm === 1 && permission.filePermission === 2) {
                            // 直接下载权限
                            buttons.push(
                                h(NButton, {
                                    size: 'medium',
                                    style: 'min-width: 120px;',
                                    onClick: () => {
                                        window.$dialog.destroyAll();
                                        onConfirm('direct', permission.fileForm, permission.filePermission, permission.inventoryId);
                                    }
                                }, { default: () => '下载' })
                            );
                        } else if (permission.fileForm === 2 && permission.filePermission === 3) {
                            // 纸质文件一次下载权限
                            buttons.push(
                                h(NButton, {
                                    size: 'medium',
                                    style: 'min-width: 140px;',
                                    onClick: () => {
                                        window.$dialog.destroyAll();
                                        onConfirm('paper-once', permission.fileForm, permission.filePermission, permission.inventoryId);
                                    }
                                }, { default: () => '纸质文件：一次下载' })
                            );
                        } else if (permission.fileForm === 1 && permission.filePermission === 3) {
                            // 电子文件一次下载权限
                            buttons.push(
                                h(NButton, {
                                    size: 'medium',
                                    style: 'min-width: 140px;',
                                    onClick: () => {
                                        window.$dialog.destroyAll();
                                        onConfirm('electronic-once', permission.fileForm, permission.filePermission, permission.inventoryId);
                                    }
                                }, { default: () => '电子文件：一次下载' })
                            );
                        }
                    }

                    // 显示动态生成的按钮
                    window.$dialog.warning({
                        title: '请选择下载方式',
                        style: `width: ${Math.max(400, buttons.length * 160)}px;`,
                        content: () => h('div', { style: 'text-align: center;' }, [
                            h('p', { style: 'margin-bottom: 16px;' }, '请选择您希望使用的下载方式：'),
                            h('div', { style: 'display: flex; gap: 12px; justify-content: center;' }, buttons)
                        ]),
                        positiveText: '取消',
                        negativeText: '',
                        onPositiveClick: () => {
                            window.$dialog.destroyAll();
                        }
                    });
                    return;
                }

                // 原有的逻辑（兼容单个权限的情况）
                const isPaperFile = fileForm === 1;
                const hasDownloadPermission = filePermission === 1;

                if (isPaperFile && hasDownloadPermission) {
                    // 纸质文件且有下载权限：显示三个选项
                    window.$dialog.warning({
                        title: '请选择下载方式',
                        style: 'width: 600px;',
                        content: () => h('div', { style: 'text-align: center;' }, [
                            h('p', { style: 'margin-bottom: 16px;' }, '请选择您希望使用的下载方式：'),
                            h('div', { style: 'display: flex; gap: 12px; justify-content: center;' }, [
                                h(NButton, {
                                    size: 'medium',
                                    style: 'min-width: 120px;',
                                    onClick: () => {
                                        window.$dialog.destroyAll();
                                        onConfirm('direct');
                                    }
                                }, { default: () => '下载' }),
                                h(NButton, {
                                    size: 'medium',
                                    style: 'min-width: 140px;',
                                    onClick: () => {
                                        window.$dialog.destroyAll();
                                        onConfirm('paper-once');
                                    }
                                }, { default: () => '纸质文件：一次下载' }),
                                h(NButton, {
                                    size: 'medium',
                                    style: 'min-width: 140px;',
                                    onClick: () => {
                                        window.$dialog.destroyAll();
                                        onConfirm('electronic-once');
                                    }
                                }, { default: () => '电子文件：一次下载' })
                            ])
                        ]),
                        positiveText: '取消',
                        negativeText: '',
                        onPositiveClick: () => {
                            window.$dialog.destroyAll();
                        }
                    });
                } else if (isPaperFile) {
                    // 纸质文件但无下载权限：显示两个选项
                    const buttons: any[] = [
                        h(NButton, {
                            size: 'medium',
                            style: 'min-width: 140px;',
                            onClick: () => {
                                window.$dialog.destroyAll();
                                onConfirm('paper-once');
                            }
                        }, { default: () => '纸质文件：一次下载' }),
                        h(NButton, {
                            size: 'medium',
                            style: 'min-width: 140px;',
                            onClick: () => {
                                window.$dialog.destroyAll();
                                onConfirm('electronic-once');
                            }
                        }, { default: () => '电子文件：一次下载' })
                    ];

                    window.$dialog.warning({
                        title: '请选择下载方式',
                        style: 'width: 500px;',
                        content: () => h('div', { style: 'text-align: center;' }, [
                            h('p', { style: 'margin-bottom: 16px;' }, '请选择您希望使用的下载方式：'),
                            h('div', { style: 'display: flex; gap: 12px; justify-content: center;' }, buttons)
                        ]),
                        positiveText: '取消',
                        negativeText: '',
                        onPositiveClick: () => {
                            window.$dialog.destroyAll();
                        }
                    });
                } else {
                    // 电子文件：显示两个选项
                    window.$dialog.warning({
                        title: '请选择下载方式',
                        style: 'width: 400px;',
                        content: '请选择您希望使用的下载方式：',
                        positiveText: '下载',
                        negativeText: '一次下载',
                        onPositiveClick: () => {
                            onConfirm('direct');
                        },
                        onNegativeClick: () => {
                            onConfirm('batch');
                        }
                    });
                }
            }
        } else {
            // 无权限，先显示确认对话框
            window.$dialog.error({
                title: type === 'view' ? '无查阅权限' : '无下载权限',
                content:
                    type === 'view'
                        ? '您当前没有查阅该文件的权限，是否发起查阅申请？'
                        : '您当前没有下载该文件的权限，是否发起下载申请？',
                positiveText: '发起申请',
                negativeText: '取消',
                onPositiveClick: () => {
                    // 确认后打开发放申请弹框
                    $alert.dialog({
                        title: '发放申请',
                        width: '60%',
                        content: import('./refer-to-from.vue'),
                        props: {
                            row,
                            type: type === 'view' ? 1 : 2,
                            fileType: fileType
                        }
                    });
                }
            });
        }
    }

    /**
     * 执行查阅操作（带权限参数）
     */
    static async executeViewWithPermission(row: any, viewPermission: { fileForm: number, filePermission: number, inventoryId: string }): Promise<void> {
        // 这里不需要 getRecord 过滤，因为 viewPermission 已经是过滤后的
        const resData = await $apis.nebula.api.v1.businessDictionary.node.isPermission({
            documentId: row.id,
            fileForm: viewPermission.fileForm,
            inventoryId: viewPermission.inventoryId,
            filePermission: viewPermission.filePermission,
            operationType: 1
        });

        if (resData.data.fileId) {
            $alert.dialog({
                title: `文件预览: ${row.name}`,
                width: '80%',
                content: import('@/components/file-preview.vue'),
                props: {
                    id: resData.data.fileId || row.fileInfo?.fileId,
                    name: row.name,
                    format: 'pdf'
                }
            });
        } else {
            window.$message.error('文件不存在');
        }
    }

    /**
     * 执行查阅操作
     */
    static async executeView(row: any): Promise<void> {
        // 调用 isPermission 接口执行查阅
        const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
            documentId: row.id,
            userId: store.userInfo.id
        });
        // 只保留 status 为 2 或 3 的记录
        const list = (res.data.list || []).filter((record: any) => record.status === 2 || record.status === 3);
        // 找到对应的查阅权限记录，并且 isUsed 不为 true
        const viewPermission = list.find((record: any) =>
        (((record.fileForm === 1 && record.filePermission === 1) ||
            (record.fileForm === 1 && record.filePermission === 2)) &&
            record.isUsed !== true)
        );

        if (viewPermission) {
            const resData = await $apis.nebula.api.v1.businessDictionary.node.isPermission({
                documentId: row.id,
                fileForm: viewPermission.fileForm,
                inventoryId: viewPermission.inventoryId,
                filePermission: viewPermission.filePermission,
                operationType: 1
            });

            if (resData.data.fileId) {
                $alert.dialog({
                    title: `文件预览: ${row.name}`,
                    width: '80%',
                    content: import('@/components/file-preview.vue'),
                    props: {
                        id: resData.data.fileId || row.fileInfo?.fileId,
                        name: row.name,
                        format: 'pdf'
                    }
                });
            } else {
                window.$message.error('文件不存在');
            }
        } else {
            window.$message.error('未找到查阅权限记录');
        }
    }

    /**
     * 执行下载操作（带权限校验）
     */
    static async executeDownload(row: any, method: string): Promise<void> {
        // 根据不同的下载方式传递不同的参数
        let fileForm: number;
        let filePermission: number;

        switch (method) {
            case 'direct':
                fileForm = 1;
                filePermission = 2;
                break;
            case 'paper-once':
                fileForm = 2;
                filePermission = 3;
                break;
            case 'electronic-once':
                fileForm = 1;
                filePermission = 3;
                break;
            case 'batch':
                fileForm = 1;
                filePermission = 2;
                break;
            default:
                fileForm = 1;
                filePermission = 2;
        }

        // 重新检查权限
        const permissionResult = await this.checkDownloadPermission(row, fileForm, filePermission);

        if (permissionResult.hasPermission) {
            // 有权限，调用 isPermission 接口执行下载
            const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
                documentId: row.id,
                userId: store.userInfo.id
            });
            // 只保留 status 为 2 或 3 的记录，但对纸质文件一次下载权限(fileForm=2, filePermission=3)使用 status > 1
            const list = (res.data.list || []).filter((record: any) => {
                // 对于 fileForm=1 且 filePermission=2，只要 status !== 4 就可以使用
                if (record.fileForm === 1 && record.filePermission === 2) {
                    return record.status !== 4;
                }
                // 对于纸质文件一次下载权限(fileForm=2, filePermission=3)使用 status > 1
                if (record.fileForm === 2 && record.filePermission === 3) {
                    return record.status > 1;
                }
                // 其他权限使用 status === 2 || status === 3
                return record.status === 2 || record.status === 3;
            });
            const resData = await $apis.nebula.api.v1.businessDictionary.node.isPermission({
                documentId: row.id,
                fileForm: fileForm,
                inventoryId: list[0]?.inventoryId || '',
                filePermission: filePermission,
                operationType: 2
            });

            if (resData.data.fileId) {
                window.$message.success('下载申请成功，请在数据导出中查看');
            } else {
                window.$message.error('下载失败，没有文件');
            }
        } else {
            // 无权限，显示申请对话框
            this.showPermissionDialog('download', false, (method: string) => this.executeDownload(row, method), row);
        }
    }

    /**
     * 直接执行下载操作（不重复校验权限）
     */
    static async executeDownloadDirect(row: any, method: string, fileForm?: number, filePermission?: number, inventoryId?: string): Promise<void> {
        // 如果没有传入参数，根据方法确定参数
        if (fileForm === undefined || filePermission === undefined) {
            switch (method) {
                case 'direct':
                    fileForm = 1;
                    filePermission = 2;
                    break;
                case 'paper-once':
                    fileForm = 2;
                    filePermission = 3;
                    break;
                case 'electronic-once':
                    fileForm = 1;
                    filePermission = 3;
                    break;
                case 'batch':
                    fileForm = 1;
                    filePermission = 2;
                    break;
                default:
                    fileForm = 1;
                    filePermission = 2;
            }
        }

        // 调用 isPermission 接口执行下载
        const resData = await $apis.nebula.api.v1.businessDictionary.node.isPermission({
            documentId: row.id,
            fileForm: fileForm,
            inventoryId: inventoryId || '',
            filePermission: filePermission,
            operationType: 2
        });

        if (resData.data.fileId) {
            window.$message.success('下载申请成功，请在数据导出中查看');
        } else {
            window.$message.error('下载失败，没有文件');
        }
    }

    /**
     * 处理查阅操作（包含权限校验和二次确认）
     */
    static async handleView(row: any, fileType: number): Promise<void> {
        try {
            // 先获取权限记录
            const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
                documentId: row.id,
                userId: store.userInfo.id
            });

            // 获取有效权限和进行中的申请，根据不同的权限类型使用不同的 status 过滤逻辑
            const validRecords = (res.data.list || []).filter((record: any) => {
                // 对于 fileForm=1 且 filePermission=1 或 filePermission=2，只要 status !== 4 就可以使用
                if (record.fileForm === 1 && (record.filePermission === 1 || record.filePermission === 2)) {
                    return record.status !== 4;
                }
                // 其他权限使用 status === 2 || status === 3
                return record.status === 2 || record.status === 3;
            });
            const ongoingRecords = (res.data.list || []).filter((record: any) => record.status === 1);

            // 检查是否有有效的查阅权限，优先选择状态为 2 或 3 的权限
            const viewPermission = validRecords.find((record: any) =>
                ((record.fileForm === 1 && record.filePermission === 1) ||
                    (record.fileForm === 1 && record.filePermission === 2)) &&
                (record.status === 2 || record.status === 3)
            );

            if (viewPermission) {
                // 有权限，直接执行查阅操作
                const resData = await $apis.nebula.api.v1.businessDictionary.node.isPermission({
                    documentId: row.id,
                    fileForm: viewPermission.fileForm,
                    inventoryId: viewPermission.inventoryId,
                    filePermission: viewPermission.filePermission,
                    operationType: 1
                });

                if (resData.data.fileId) {
                    $alert.dialog({
                        title: `文件预览: ${row.name}`,
                        width: '80%',
                        content: import('@/components/file-preview.vue'),
                        props: {
                            id: resData.data.fileId || row.fileInfo?.fileId,
                            name: row.name,
                            format: 'pdf'
                        }
                    });
                } else {
                    window.$message.error('文件不存在');
                }
            } else {
                // 无有效权限，检查是否有进行中的查阅申请
                const ongoingViewRecords = ongoingRecords.filter((record: any) =>
                    (record.fileForm === 1 && record.filePermission === 1) ||
                    (record.fileForm === 1 && record.filePermission === 2)
                );

                if (ongoingViewRecords.length > 0) {
                    window.$message.warning('当前已有申请流程，请在流程完成后重试');
                    return;
                }
                // 无权限且无进行中的申请，显示申请对话框
                this.showPermissionDialog('view', false, () => this.executeView(row), row, fileType);
            }
        } catch (error) {
            console.error('查阅操作处理失败:', error);
        }
    }

    /**
     * 处理下载操作
     */
    static async handleDownload(row: any, fileType: number): Promise<void> {
        try {
            // 先获取所有权限记录
            const res = await $apis.nebula.api.v1.businessDictionary.node.getRecord({
                documentId: row.id,
                userId: store.userInfo.id
            });

            // 分别获取有效权限和进行中的申请，根据不同的权限类型使用不同的 status 过滤逻辑
            const validRecords = (res.data.list || []).filter((record: any) => {
                // 对于 fileForm=1 且 filePermission=2，只要 status !== 4 就可以使用
                if (record.fileForm === 1 && record.filePermission === 2) {
                    return record.status !== 4;
                }
                // 对于纸质文件一次下载权限(fileForm=2, filePermission=3)使用 status > 1
                if (record.fileForm === 2 && record.filePermission === 3) {
                    return record.status > 1;
                }
                // 其他权限使用 status === 2 || status === 3
                return record.status === 2 || record.status === 3;
            });
            const allOngoingRecords = (res.data.list || []).filter((record: any) => record.status === 1);
            // 遍历所有有效记录，找出所有不同的下载权限组合并去重
            const downloadPermissions: Array<{ fileForm: number, filePermission: number, inventoryId: string }> = [];
            const seen = new Set<string>(); // 用于去重

            for (const record of validRecords) {
                // 只收集下载相关的权限（1,2 或 1,3 或 2,3）
                if (record.fileForm === 1 && record.filePermission === 2) {
                    // 对于 fileForm=1 且 filePermission=2，不判断 isUsed
                    const key = `${record.fileForm}-${record.filePermission}`;
                    if (!seen.has(key)) {
                        seen.add(key);
                        downloadPermissions.push({
                            fileForm: record.fileForm,
                            filePermission: record.filePermission,
                            inventoryId: record.inventoryId
                        });
                    }
                } else if (((record.fileForm === 1 && record.filePermission === 3) ||
                    (record.fileForm === 2 && record.filePermission === 3)) &&
                    record.isUsed !== true) {
                    // 对于其他权限类型，判断 isUsed !== true
                    const key = `${record.fileForm}-${record.filePermission}`;
                    if (!seen.has(key)) {
                        seen.add(key);
                        downloadPermissions.push({
                            fileForm: record.fileForm,
                            filePermission: record.filePermission,
                            inventoryId: record.inventoryId
                        });
                    }
                }
            }
            // 检查是否有已使用的下载权限（用于判断是否所有权限都已使用）
            console.log(res.data.list, 'res.data.list');
            const usedDownloadPermissions = (res.data.list || []).filter((record: any) => {
                // 对于 fileForm=1 且 filePermission=2，不判断 isUsed
                if (record.fileForm === 1 && record.filePermission === 2) {
                    if (record.status === 2 || record.status === 3) {
                        return false; // 不认为是已使用的权限
                    } else {
                        return true;
                    }
                }
                // 对于其他权限类型，判断 isUsed === true
                return ((record.fileForm === 1 && record.filePermission === 3) ||
                    (record.fileForm === 2 && record.filePermission === 3)) &&
                    record.isUsed === true;
            });
            console.log(usedDownloadPermissions, 'usedDownloadPermissions');

            // 检查是否有进行中的下载申请（只过滤下载相关的权限）
            const ongoingDownloadRecords = allOngoingRecords.filter((record: any) =>
                (record.fileForm === 1 && record.filePermission === 2) ||
                (record.fileForm === 1 && record.filePermission === 3) ||
                (record.fileForm === 2 && record.filePermission === 3)
            );

            // 1. 有可用下载权限，直接下载
            if (downloadPermissions.length > 0) {
                const permission = downloadPermissions[0];
                let method = 'direct';
                if (permission.fileForm === 1 && permission.filePermission === 2) {
                    method = 'direct';
                } else if (permission.fileForm === 1 && permission.filePermission === 3) {
                    method = 'electronic-once';
                } else if (permission.fileForm === 2 && permission.filePermission === 3) {
                    method = 'paper-once';
                }
                this.executeDownloadDirect(row, method, permission.fileForm, permission.filePermission, permission.inventoryId);
                return;
            }
            // 2. 有进行中的下载申请
            if (ongoingDownloadRecords.length > 0) {
                window.$message.warning('当前已有申请流程，请在流程完成后重试');
                return;
            }
            // 3. 有已用过的下载权限
            if (usedDownloadPermissions.length > 0) {
                const adminCheck = await this.checkIsAdmin();
                if (!adminCheck.isAdmin) {
                    window.$message.warning('您已经下载过该文件，无法再次申请下载');
                    return;
                } else {
                    // 管理员：显示已使用的权限信息，询问是否申请新权限
                    const usedPermissionsText = usedDownloadPermissions.map((record: any) => {
                        if (record.fileForm === 1 && record.filePermission === 3) {
                            return '电子文件：一次下载';
                        } else if (record.fileForm === 2 && record.filePermission === 3) {
                            return '纸质文件：一次下载';
                        }
                    }).join('、');

                    window.$dialog.warning({
                        title: '权限已使用',
                        content: `您已经使用过以下下载权限：\n${usedPermissionsText}\n\n是否要申请新的下载权限？`,
                        positiveText: '申请新权限',
                        negativeText: '取消',
                        onPositiveClick: () => {
                            // 确认后打开发放申请弹框
                            $alert.dialog({
                                title: '发放申请',
                                width: '60%',
                                content: import('./refer-to-from.vue'),
                                props: {
                                    row,
                                    type: 2,
                                    fileType: fileType
                                }
                            });
                        }
                    });
                    return;
                }
            }
            // 4. 都没有，弹出申请窗口
            this.showPermissionDialog('download', false, (method: string) => this.executeDownload(row, method), row, undefined, undefined, fileType);
        } catch (error) {
            console.error('下载操作处理失败:', error);
        }
    }
}


const filePermissionUtils = new FilePermissionUtils();

export const globalHandleView = (row: any, fileType: number): Promise<void> => {
    return filePermissionUtils.handleView(row, fileType);
}
