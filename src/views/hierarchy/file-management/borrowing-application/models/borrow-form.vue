<template>
    <alert-content
        :buttons="buttons"
        :on-default-save="() => handleSave()"
        :show-default-buttons="!isRecycle && !isPreview"
    >
        <n-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-placement="left"
            require-mark-placement="left"
            :label-width="80"
            :show-feedback="false"
            :disabled="isPreview"
        >
            <n-grid :cols="24" :x-gap="10" :y-gap="10">
                <n-form-item-gi label="申请人" :span="12">
                    <n-input v-model:value="formData.userNickname" readonly />
                </n-form-item-gi>

                <n-form-item-gi label="申请日期" :span="12">
                    <n-input :value="formData.approvalApplyTime" readonly></n-input>
                </n-form-item-gi>

                <n-form-item-gi label="借阅日期" path="borrowPeriod" :span="24">
                    <n-date-picker
                        v-model:value="formData.borrowPeriod"
                        type="daterange"
                        format="yyyy-MM-dd"
                        placeholder="开始日期-结束日期"
                        class="w-full"
                    />
                </n-form-item-gi>

                <n-form-item-gi :span="12" label="借阅原因" path="reason">
                    <n-select
                        v-model:value="formData.reason"
                        :options="reasonOptions"
                        placeholder="请选择发放原因"
                        clearable
                    />
                </n-form-item-gi>
                <n-form-item-gi v-if="formData.reason === 5" :span="24" label="其他原因" path="otherReason">
                    <n-input
                        v-model:value="formData.otherReason"
                        maxlength="50"
                        placeholder="请输入其他借阅原因"
                        show-count
                    />
                </n-form-item-gi>
                <n-form-item-gi :span="24" path="documents">
                    <div class="flex-v w-100%">
                        <div class="flex justify-between">
                            <span class="ml-2px mb-10px required-field">借阅清单</span>
                            <n-button v-if="isRecycle" type="primary" size="small" @click="handleRecycleAll">
                                一键回收
                            </n-button>
                            <n-button v-if="!isPreview" type="primary" size="small" @click="handleAddFile">
                                添加文件
                            </n-button>
                        </div>
                        <vxe-table
                            ref="tableRef"
                            class="w-100%"
                            :border="true"
                            show-overflow
                            auto-resize
                            :edit-rules="validRules"
                            :valid-config="{ showMessage: false }"
                            :edit-config="{
                                trigger: 'click',
                                mode: 'cell',
                                enabled: !isPreview
                            }"
                            :data="formData.documents"
                        >
                            <vxe-column type="seq" title="序号" width="70" fixed="left"></vxe-column>
                            <vxe-column
                                field="documentValidity"
                                title="文件有效性"
                                minWidth="140"
                                :edit-render="{ name: 'VxeSelect', options: documentValidityOptions }"
                            ></vxe-column>
                            <vxe-column
                                field="documentModuleType"
                                title="文件类型"
                                minWidth="120"
                                :edit-render="{
                                    name: 'VxeSelect',
                                    options: typeOptions,
                                    events: { change: handleDocumentModuleTypeChange }
                                }"
                            ></vxe-column>
                            <vxe-column
                                field="documentCategoryId"
                                title="文件类别"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="
                                    ({ row }) =>
                                        formatOptionLabel(row.id, 'documentCategoryIdOptions', row.documentCategoryId)
                                "
                            >
                                <template #edit="{ row }">
                                    <vxe-tree-select
                                        v-model="row.documentCategoryId"
                                        :options="getRowOptions(row.id, 'documentCategoryIdOptions')"
                                        @change="handleDocumentCategoryIdChange($event, row)"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentId"
                                title="文件名称"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'documentIdOptions', row.documentId)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentId"
                                        :options="getRowOptions(row.id, 'documentIdOptions')"
                                        @change="handleDocumentIdChange($event, row)"
                                        clearable
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentNo"
                                title="文件编号"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="({ row }) => formatOptionLabel(row.id, 'documentNoOptions', row.documentNo)"
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentNo"
                                        :options="getRowOptions(row.id, 'documentNoOptions')"
                                        @change="handleDocumentNoChange($event, row)"
                                        clearable
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                field="documentVersionNo"
                                title="版本/版次"
                                minWidth="120"
                                :edit-render="{}"
                                :formatter="
                                    ({ row }) =>
                                        formatOptionLabel(row.id, 'documentVersionNoOptions', row.documentVersionNo)
                                "
                            >
                                <template #edit="{ row }">
                                    <vxe-select
                                        v-model="row.documentVersionNo"
                                        :options="getRowOptions(row.id, 'documentVersionNoOptions')"
                                    />
                                </template>
                            </vxe-column>
                            <vxe-column
                                v-if="!isPreview || isRecycle"
                                field="todo"
                                title="操作"
                                width="80"
                                fixed="right"
                            >
                                <template v-slot="{ row }">
                                    <!-- borrowStatus  1 借阅中  2 回收中  3 已回收 -->
                                    <n-button
                                        v-if="isRecycle && row.borrowStatus === 1"
                                        size="tiny"
                                        type="warning"
                                        @click="handleRecycle(row)"
                                    >
                                        回收
                                    </n-button>
                                    <n-tag
                                        v-if="isRecycle && row.borrowStatus !== 1"
                                        :type="row.borrowStatus === 2 ? 'primary' : 'success'"
                                        size="small"
                                        round
                                        :bordered="false"
                                    >
                                        {{ row.borrowStatus === 2 ? '回收中' : '已回收' }}
                                    </n-tag>
                                    <n-button
                                        v-if="!isPreview && !isRecycle"
                                        size="tiny"
                                        type="error"
                                        @click="handleRemoveFile(row)"
                                    >
                                        删除
                                    </n-button>
                                </template>
                            </vxe-column>
                        </vxe-table>
                    </div>
                </n-form-item-gi>
            </n-grid>
        </n-form>
    </alert-content>
</template>

<script setup lang="ts">
import { NButton, NInput, NSelect, type FormRules } from 'naive-ui';
import { DocumentLibraryData, DocumentLibraryForm } from '@/api/apis/nebula/api/v1/document-library/loans';
import { VxeTable, VxeColumn, VxeTablePropTypes } from 'vxe-table';
import { VxeSelect, VxeTreeSelect } from 'vxe-pc-ui';
import { RowVO } from '@/api/sass/api/v1/dict';
import dayjs from 'dayjs';
import { ButtonsConfig } from '@/components/alert-content.vue';
import { nanoid } from 'nanoid';
import useStore from '@/store/modules/main';

const props = defineProps<{
    row?: DocumentLibraryData;
    modelValue?: any;
    isPreview?: boolean;
    isRecycle?: boolean;
}>();

const store = useStore();

const buttons: ButtonsConfig = {
    save: {
        text: '提交'
    },
    extra: {
        saveTemp: {
            text: '暂存',
            type: 'success',
            onClick: () => {
                return handleSaveTemp();
            },
            emit: 'temp',
            autoClose: true
        }
    }
};

// 处理暂存操作
const handleSaveTemp = async (): Promise<void> => {
    return handleSave(true);
};

// 表单数据
const formData = ref<DocumentLibraryForm>({
    approvalApplyTime: dayjs().format('YYYY-MM-DD'),
    userNickname: store.userInfo.nickname,
    borrowPeriod: null,
    reason: null,
    otherReason: '',
    documents: []
});

// 选项数据管理器类
class RowOptionsManager {
    private optionsMap: Map<string, any> = new Map();

    // 获取行选项数据
    getRowOptions(rowId: string, optionType: string) {
        const rowOptions = this.optionsMap.get(rowId);
        return rowOptions?.[optionType] || [];
    }

    // 设置行选项数据
    setRowOptions(rowId: string, optionType: string, options: any[]) {
        if (!this.optionsMap.has(rowId)) {
            this.optionsMap.set(rowId, {});
        }
        const rowOptions = this.optionsMap.get(rowId);
        rowOptions[optionType] = options;
    }

    // 清理行选项数据
    clearRowOptions(rowId: string) {
        this.optionsMap.delete(rowId);
    }

    // 检查是否存在指定的 rowId
    hasRowOptions(rowId: string) {
        return this.optionsMap.has(rowId);
    }

    // 初始化行选项
    initRowOptions(rowId: string) {
        this.optionsMap.set(rowId, {
            documentOptions: [],
            documentCategoryIdOptions: [],
            documentIdOptions: [],
            documentNoOptions: [],
            documentVersionNoOptions: []
        });
    }
}

const rowOptionsManager = new RowOptionsManager();

// 借阅原因选项
const reasonOptions = [
    { label: '项目需要 / 研究', value: 1 },
    { label: '问题调查 / 分析', value: 2 },
    { label: '审计 / 检查准备', value: 3 },
    { label: '培训 / 学习需要', value: 4 },
    { label: '其他', value: 5 }
];

// 表单验证规则
const rules: FormRules = {
    borrowPeriod: { required: true, message: '请输入借阅日期', trigger: ['blur', 'change'], type: 'array' },
    reason: { required: true, message: '请选择借阅原因', trigger: ['blur', 'change'], type: 'number' },
    otherReason: { required: true, message: '请输入其他借阅原因', trigger: ['blur', 'change'] },
    documents: {
        required: true,
        validator: () => {
            if (!formData.value.documents?.length) {
                return new Error('请填写借阅清单（至少一个文件）');
            }
            return true;
        }
    }
};

// 表格验证
const validRules = ref<VxeTablePropTypes.EditRules<RowVO>>({
    documentValidity: [{ required: true, message: '请选择文件有效性' }],
    documentModuleType: [{ required: true, message: '请选择文件类型' }],
    documentCategoryId: [{ required: true, message: '请选择文件分类' }],
    documentId: [{ required: true, message: '请输入文件名称' }],
    documentNo: [{ required: true, message: '请输入文件编号' }],
    documentVersionNo: [{ required: true, message: '请输入文件版本' }]
});

// 添加文件
const handleAddFile = () => {
    const newID = nanoid();
    formData.value.documents?.push({
        id: newID,
        documentModuleType: '',
        documentCategoryId: null,
        documentId: '',
        documentNo: null,
        documentVersionNo: '',
        documentValidity: 3
    });

    // 为新行初始化选项数据
    initRowOptions(newID);
};

// 删除文件
const handleRemoveFile = (row: any) => {
    const index = formData.value.documents?.findIndex((item) => item.id === row.id);
    if (index !== undefined && index >= 0) {
        formData.value.documents?.splice(index, 1);
        // 清理对应的选项数据
        rowOptionsManager.clearRowOptions(row.id);
    }
};

// 初始化行选项数据
const initRowOptions = (rowId: string) => {
    rowOptionsManager.initRowOptions(rowId);
};

// 获取指定行的选项数据
const getRowOptions = (rowId: string, optionType: string): any[] => {
    return rowOptionsManager.getRowOptions(rowId, optionType);
};

// 格式化选项标签 - 将 id 转换为对应的 label 显示
const formatOptionLabel = (rowId: string, optionType: string, value: string): string => {
    if (!value) return '';
    const options = rowOptionsManager.getRowOptions(rowId, optionType);
    const option = options.find((opt: any) => opt.value === value);
    return option ? option.label : value;
};

// 生成 autoFormData
const generateAutoFormData = (documents: any[]): any => {
    return {
        userNickname: formData.value.userNickname,
        approvalApplyTime: formData.value.approvalApplyTime,
        borrowPeriod: formData.value.borrowPeriod,
        reason: formData.value.reason,
        otherReason: formData.value.otherReason,
        documents: documents.map((item: any) => {
            return {
                ...item,
                documentCategoryId: formatOptionLabel(item.id, 'documentCategoryIdOptions', item.documentCategoryId),
                documentId: formatOptionLabel(item.id, 'documentIdOptions', item.documentId)
            };
        })
    };
};
// 状态选项
const documentValidityOptions = [
    { label: '作废', value: -1 },
    { label: '即将作废', value: 1 },
    { label: '即将实施', value: 2 },
    { label: '有效', value: 3 },
    { label: '拟修订', value: 4 }
];

// 类型选项
const typeOptions = [
    { label: '内部文件', value: 2 },
    { label: '外部文件', value: 3 }
];

// 文件数据管理器类
class DocumentDataManager {
    // 获取去重选项
    private getUniqueOptions(data: any[], field: string) {
        return data
            .filter((item: any, index: number, self: any[]) => {
                return self.findIndex((i: any) => i[field] === item[field]) === index;
            })
            .map((item: any) => ({
                ...item,
                label: item[field],
                value: field === 'documentName' ? item.documentId : item[field]
            }));
    }

    // 更新行字段值
    private updateRowFields(rowId: string, fields: any) {
        const fileItem = formData.value.documents?.find((item) => item.id === rowId);
        if (fileItem) {
            Object.assign(fileItem, fields);
        }
    }

    // 获取选项标签
    private getOptionLabel(rowId: string, optionType: string, value: string) {
        const options = rowOptionsManager.getRowOptions(rowId, optionType);
        const option = options.find((opt: any) => opt.value === value);
        return option ? option.label : '';
    }

    // 简化的文件类别变更处理
    async handleCategoryChange(row: any, categoryValue: string, isPreload = false) {
        // 只有在非预加载情况下才更新相关字段值
        if (!isPreload) {
            this.updateRowFields(row.id, {
                documentCategoryName: this.getOptionLabel(row.id, 'documentCategoryIdOptions', categoryValue),
                documentId: '',
                documentName: '',
                documentNo: '',
                documentVersionNo: ''
            });
        }

        // 查询允许借阅的文件
        await $apis.nebula.api.v1.documentLibrary.allow
            .allowBorrow({
                documentValidity: row.documentValidity, // 文件有效性 3:有效 -1:作废
                documentModuleType: row.documentModuleType, // 文件模块，1：书籍  2：内部文档  3：外部文档
                documentCategoryId: categoryValue // 文档类别 id
            })
            .then((res) => {
                const rowData = res.data.list;
                if (rowData?.length > 0) {
                    // 更新选项数据
                    rowOptionsManager.setRowOptions(row.id, 'documentOptions', rowData);
                    rowOptionsManager.setRowOptions(
                        row.id,
                        'documentIdOptions',
                        this.getUniqueOptions(rowData, 'documentName')
                    );
                    rowOptionsManager.setRowOptions(
                        row.id,
                        'documentNoOptions',
                        this.getUniqueOptions(rowData, 'documentNo')
                    );
                } else {
                    rowOptionsManager.setRowOptions(row.id, 'documentOptions', []);
                    rowOptionsManager.setRowOptions(row.id, 'documentIdOptions', []);
                    rowOptionsManager.setRowOptions(row.id, 'documentNoOptions', []);
                    window.$message.warning('当前文件类别下不存在文件');
                }
            });
    }

    // 简化的文件名称变更处理
    async handleDocumentIdChange(row: any, documentIdValue: string) {
        // 更新相关字段值
        this.updateRowFields(row.id, {
            documentName: this.getOptionLabel(row.id, 'documentIdOptions', documentIdValue),
            documentVersionNo: ''
        });

        // 更新文件编号选项
        const documentOptions = rowOptionsManager.getRowOptions(row.id, 'documentOptions');
        if (documentIdValue) {
            // 选择了文件名称，过滤文件编号选项
            const filteredOptions = documentOptions.filter((item: any) => item.documentId === documentIdValue);
            rowOptionsManager.setRowOptions(
                row.id,
                'documentNoOptions',
                this.getUniqueOptions(filteredOptions, 'documentNo')
            );

            // 更新版本选项
            const versionOptions = filteredOptions.filter(
                (item: any) => item.documentNo === row.documentNo && item.documentId === documentIdValue
            );
            rowOptionsManager.setRowOptions(
                row.id,
                'documentVersionNoOptions',
                this.getUniqueOptions(versionOptions, 'documentVersionNo')
            );
        } else {
            // 清除了文件名称，恢复所有文件编号选项
            rowOptionsManager.setRowOptions(
                row.id,
                'documentNoOptions',
                this.getUniqueOptions(documentOptions, 'documentNo')
            );

            // 清除版本选项
            rowOptionsManager.setRowOptions(row.id, 'documentVersionNoOptions', []);
        }
    }

    // 简化的文件编号变更处理
    async handleDocumentNoChange(row: any, documentNoValue: string) {
        // 更新相关字段值
        this.updateRowFields(row.id, {
            documentVersionNo: ''
        });

        // 更新文件名称选项
        const documentOptions = rowOptionsManager.getRowOptions(row.id, 'documentOptions');
        if (documentNoValue) {
            // 选择了文件编号，过滤文件名称选项
            const filteredOptions = documentOptions.filter((item: any) => item.documentNo === documentNoValue);
            rowOptionsManager.setRowOptions(
                row.id,
                'documentIdOptions',
                this.getUniqueOptions(filteredOptions, 'documentName')
            );

            // 更新版本选项
            const versionOptions = filteredOptions.filter(
                (item: any) => item.documentNo === documentNoValue && item.documentId === row.documentId
            );
            rowOptionsManager.setRowOptions(
                row.id,
                'documentVersionNoOptions',
                this.getUniqueOptions(versionOptions, 'documentVersionNo')
            );
        } else {
            // 清除了文件编号，恢复所有文件名称选项
            rowOptionsManager.setRowOptions(
                row.id,
                'documentIdOptions',
                this.getUniqueOptions(documentOptions, 'documentName')
            );

            // 清除版本选项
            rowOptionsManager.setRowOptions(row.id, 'documentVersionNoOptions', []);
        }
    }
}

const documentDataManager = new DocumentDataManager();

const handleDocumentModuleTypeChange = async (row: any, rowIndex: any, isPreload = false) => {
    // 更新相关字段值
    const fileItem = formData.value.documents?.find((item) => item.id === row.row.id);
    if (fileItem) {
        fileItem.documentModuleName = typeOptions.find((item) => item.value === rowIndex.value)?.label;
        // 只有在非预加载情况下才清除相关字段
        if (!isPreload) {
            fileItem.documentCategoryId = '';
            fileItem.documentCategoryName = '';
            fileItem.documentId = '';
            fileItem.documentName = '';
            fileItem.documentNo = '';
            fileItem.documentVersionNo = '';
        }
    }

    try {
        const dict = await api.sass.api.v1.dict.get('file_business_dictionary');
        let categoryId = '';
        switch (rowIndex.value) {
            case 2:
                categoryId = dict.data.data[0].extra.fileCategory.internal;
                break;
            case 3:
                categoryId = dict.data.data[0].extra.fileCategory.external;
                break;
        }

        if (!categoryId) {
            window.$message.error('获取文件类别树字典数据失败');
            return;
        }

        const res = await $apis.nebula.api.v1.businessDictionary.node.tree({
            id: categoryId
        });

        // 更新文件类别选项
        const treeData = $utils.treeData.convertTreeData(res.data);
        rowOptionsManager.setRowOptions(row.row.id, 'documentCategoryIdOptions', treeData);
    } catch (error) {
        window.$message.error('获取文件类别失败');
    }
};

// 文件类别变更处理
const handleDocumentCategoryIdChange = async (event: any, row: any, isPreload = false) => {
    const categoryValue = event?.value || event;
    // 传递 isPreload 参数给 handleCategoryChange 方法
    await documentDataManager.handleCategoryChange(row, categoryValue, isPreload);
};

// 文件名称变更处理
const handleDocumentIdChange = async (event: any, row: any) => {
    const documentIdValue = event?.value;
    await documentDataManager.handleDocumentIdChange(row, documentIdValue);
};

// 文件编号变更处理
const handleDocumentNoChange = async (event: any, row: any) => {
    const documentNoValue = event?.value;
    await documentDataManager.handleDocumentNoChange(row, documentNoValue);
};

// 回收审批
const handleRecycleApproval = async (data: any) => {
    // 根据 data 参数中的 documentId 和 documentVersionNo 来过滤出需要回收的文档
    const filteredDocuments = formData.value.documents.filter((item: any) => {
        return data.some((d: any) => {
            return d.documentId === item.documentId && d.documentVersionNo === item.documentVersionNo;
        });
    });
    const autoFormData = generateAutoFormData(filteredDocuments);
    window.$dialog.warning({
        title: '撤销',
        content: `确认后回收当前流程，是否确认？`,
        positiveText: '确认',
        negativeText: '取消',
        onPositiveClick: async () => {
            const approvalData = JSON.stringify({
                businessId: 'FILE_BORROW_RECLAIM',
                version: '1.0.0',
                borrowRecordId: props.row?.id,
                recycleData: data,
                data: autoFormData
            });

            await $hooks.useApprovalProcess('FILE_BORROW_RECLAIM', approvalData);

            // 更新借阅清单
            if (props.row?.id) {
                await $utils.loading.show({
                    text: '系统处理中...',
                    duration: 1500
                }).promise;
                await getDocuments(props.row.id);
                window.$message.success(`回收成功`);
            }
        }
    });
};
// 回收操作
const handleRecycle = (row: any) => {
    // 获取单行的文档数据
    const recycleData = {
        documentId: row.documentId,
        documentVersionNo: row.documentVersionNo
    };
    handleRecycleApproval([recycleData]);
};

const handleRecycleAll = () => {
    // 获取所有文件的文档数据
    const recycleAllData = formData.value.documents
        .filter((item: any) => item.borrowStatus === 1)
        .map((item) => ({
            documentId: item.documentId,
            documentVersionNo: item.documentVersionNo
        }));
    if (recycleAllData.length === 0) {
        window.$message.warning('没有需要回收的文件');
        return;
    }
    handleRecycleApproval(recycleAllData);
};

// 保存表单
const formRef = ref();
const tableRef = ref();
const handleSave = async (temp = false): Promise<void> => {
    try {
        await formRef.value?.validate();
        await $utils.vxeTableConfig.tableValid(tableRef);
    } catch (err: any) {
        // 用来提示表单必填错误
        window.$message.error(err[0][0].message);
        return Promise.reject();
    }

    if (formData.value.reason !== 5) {
        formData.value.otherReason = '';
    }

    const _data: any = {
        reason: formData.value.reason,
        otherReason: formData.value.otherReason,
        borrowTime: formData.value.borrowPeriod?.[0],
        dueTime: formData.value.borrowPeriod?.[1],
        documents: formData.value.documents.map((item) => {
            return {
                documentId: item.documentId,
                documentModuleType: item.documentModuleType,
                documentVersionNo: item.documentVersionNo
            };
        })
    };
    if (formData.value.id) _data.id = formData.value.id;

    let borrowRecordId;
    if (!temp) {
        _data.approvalStatus = -1;
    }
    if (props.row?.id) {
        await $apis.nebula.api.v1.documentLibrary.loans.update(_data);
        borrowRecordId = props.row.id;
    } else {
        await $apis.nebula.api.v1.documentLibrary.loans.create(_data).then((res) => {
            borrowRecordId = res.data.id;
        });
    }

    if (temp) {
        window.$message.success('提交成功');
    } else {
        // 提交审批
        const autoFormData = generateAutoFormData(formData.value.documents);
        const approvalData = JSON.stringify({
            businessId: 'FILE_BORROW',
            version: '1.0.0',
            borrowRecordId,
            data: autoFormData
        });

        await $hooks.useApprovalProcess('FILE_BORROW', approvalData);
    }
};

// 获取借阅清单
const getDocuments = async (id: string) => {
    const res = await $apis.nebula.api.v1.documentLibrary.loans.documents(id);
    const documents = res.data.data.map((item: any) => {
        return {
            ...item,
            id: nanoid()
        };
    });

    // 为每个文档项预加载数据
    for (const doc of documents) {
        if (doc.id) {
            // 初始化行选项数据
            initRowOptions(doc.id);

            // 根据 documentModuleType 获取文件类别树数据
            if (doc.documentModuleType) {
                // 创建模拟的 row 和 rowIndex 对象以复用 handleDocumentModuleTypeChange 方法
                const row = { row: doc };
                const rowIndex = { value: doc.documentModuleType };

                // 调用现有的 handleDocumentModuleTypeChange 方法获取文件类别树数据
                // 传入一个标志位，表示这是预加载数据，不要清除已有字段
                await handleDocumentModuleTypeChange(row, rowIndex, true);

                // 根据 documentValidity、documentModuleType、documentCategoryId 获取允许借阅的文件列表数据
                if (doc.documentCategoryId) {
                    // 调用现有的 handleDocumentCategoryIdChange 方法获取允许借阅的文件列表数据
                    // 传入一个标志位，表示这是预加载数据，不要清除已有字段
                    await handleDocumentCategoryIdChange(doc.documentCategoryId, doc, true);
                }
            }
        }
    }

    // 设置表单数据，触发重新渲染
    formData.value.documents = documents;
};

// 初始化数据
onMounted(async () => {
    if (props.modelValue) {
        formData.value = props.modelValue;
    }
    if (props.row) {
        // 编辑或详情模式下初始化表单数据
        formData.value = {
            id: props.row.id,
            userNickname: props.row.userNickname,
            approvalApplyTime: dayjs(props.row.approvalApplyTime).format('YYYY-MM-DD'),
            reason: props.row.reason || 0,
            otherReason: props.row.otherReason,
            borrowPeriod: [props.row.borrowTime as number, props.row.dueTime as number],
            documents: []
        };

        // 获取借阅清单数据
        if (props.row.id) {
            await getDocuments(props.row.id);
        }
    }
});
</script>

<style scoped lang="less">
/* 添加必填星号样式 */
.required-field::before {
    content: '*';
    color: var(--n-asterisk-color);
    margin-right: 4px;
}
.placeholder-text {
    color: #c0c4cc;
    font-style: italic;
}
</style>
